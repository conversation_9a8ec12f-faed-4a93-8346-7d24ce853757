# MongoDB Setup Guide for RTRDA Project

This guide provides step-by-step instructions for setting up and configuring MongoDB integration in the RTRDA project.

## Prerequisites

- Python 3.8 or higher
- Django 5.0.13
- MongoDB 4.4 or higher
- Access to MongoDB instance (local or cloud)

## Installation Steps

### 1. Install MongoDB Dependencies

The required packages have already been added to `requirements.txt`:

```bash
pip install -r requirements.txt
```

This will install:
- `pymongo==4.10.1` - MongoDB Python driver
- `mongoengine==0.29.1` - MongoDB ODM for Python

### 2. MongoDB Server Setup

#### Option A: Local MongoDB Installation

1. **Download and Install MongoDB:**
   - Visit [MongoDB Download Center](https://www.mongodb.com/try/download/community)
   - Download MongoDB Community Server for your operating system
   - Follow installation instructions for your platform

2. **Start MongoDB Service:**
   ```bash
   # Windows (as Administrator)
   net start MongoDB
   
   # macOS/Linux
   sudo systemctl start mongod
   ```

3. **Verify Installation:**
   ```bash
   mongo --version
   ```

#### Option B: MongoDB Atlas (Cloud)

1. **Create MongoDB Atlas Account:**
   - Visit [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
   - Sign up for a free account

2. **Create a Cluster:**
   - Follow the Atlas setup wizard
   - Choose the free tier (M0)
   - Select your preferred cloud provider and region

3. **Configure Network Access:**
   - Add your IP address to the IP whitelist
   - For development, you can use `0.0.0.0/0` (not recommended for production)

4. **Create Database User:**
   - Go to Database Access
   - Create a new user with read/write permissions
   - Note down the username and password

5. **Get Connection String:**
   - Click "Connect" on your cluster
   - Choose "Connect your application"
   - Copy the connection string

### 3. Environment Configuration

Create or update your `.env` file with MongoDB configuration:

```bash
# MongoDB Configuration
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=rtrda_mongodb
MONGODB_USERNAME=your_username
MONGODB_PASSWORD=your_password
MONGODB_AUTH_SOURCE=admin

# For MongoDB Atlas, use the connection string format:
# MONGODB_HOST=cluster0.xxxxx.mongodb.net
# MONGODB_PORT=27017
# MONGODB_USERNAME=your_atlas_username
# MONGODB_PASSWORD=your_atlas_password
# MONGODB_AUTH_SOURCE=admin

# Connection Pool Settings (optional)
MONGODB_MAX_POOL_SIZE=50
MONGODB_MIN_POOL_SIZE=5
MONGODB_MAX_IDLE_TIME_MS=30000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000
MONGODB_SOCKET_TIMEOUT_MS=20000
MONGODB_CONNECT_TIMEOUT_MS=20000
```

### 4. Django Configuration

The MongoDB app has already been added to `INSTALLED_APPS` in `settings.py`:

```python
INSTALLED_APPS = [
    # ... other apps
    "mongodb_app",
]
```

### 5. Test the Connection

Create a simple test script to verify MongoDB connectivity:

```python
# test_mongodb_connection.py
import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'RTRDA.settings')
django.setup()

from utils.mongodb_connection import mongodb_manager

def test_connection():
    try:
        # Test connection
        if mongodb_manager.test_connection():
            print("✅ MongoDB connection successful!")
            
            # Get database info
            client = mongodb_manager.get_client()
            db = mongodb_manager.get_database()
            
            print(f"Connected to database: {db.name}")
            print(f"Server info: {client.server_info()['version']}")
            
            # List collections
            collections = db.list_collection_names()
            print(f"Available collections: {collections}")
            
        else:
            print("❌ MongoDB connection failed!")
            
    except Exception as e:
        print(f"❌ Error testing MongoDB connection: {e}")

if __name__ == "__main__":
    test_connection()
```

Run the test:
```bash
python test_mongodb_connection.py
```

### 6. Run Database Migrations

Although MongoDB doesn't require traditional migrations, run Django migrations for the main database:

```bash
python manage.py migrate
```

### 7. Create Superuser

Create a Django superuser for API access:

```bash
python manage.py createsuperuser
```

### 8. Start the Development Server

```bash
python manage.py runserver
```

### 9. Test API Endpoints

You can now test the MongoDB API endpoints:

```bash
# Get JWT token first
curl -X POST http://localhost:8000/api/token/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "your_password"}'

# Use the token to access MongoDB endpoints
curl -X GET http://localhost:8000/api/mongo-users/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Verification Checklist

- [ ] MongoDB server is running
- [ ] Environment variables are configured
- [ ] Django server starts without errors
- [ ] MongoDB connection test passes
- [ ] API endpoints are accessible
- [ ] JWT authentication works
- [ ] CRUD operations work through API

## Troubleshooting

### Common Issues

1. **Connection Refused Error:**
   - Ensure MongoDB server is running
   - Check host and port configuration
   - Verify firewall settings

2. **Authentication Failed:**
   - Verify username and password
   - Check authentication source
   - Ensure user has proper permissions

3. **Import Errors:**
   - Ensure all dependencies are installed
   - Check Python path and virtual environment

4. **Timeout Errors:**
   - Increase timeout values in configuration
   - Check network connectivity
   - Verify MongoDB server performance

### Debug Mode

Enable debug logging for MongoDB operations by adding to `settings.py`:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'mongodb_operations': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

## Production Considerations

1. **Security:**
   - Use strong passwords
   - Enable authentication
   - Configure SSL/TLS
   - Restrict network access

2. **Performance:**
   - Create appropriate indexes
   - Monitor connection pool usage
   - Set up replica sets for high availability

3. **Backup:**
   - Set up regular backups
   - Test backup restoration
   - Consider point-in-time recovery

4. **Monitoring:**
   - Monitor database performance
   - Set up alerts for errors
   - Track resource usage

## Next Steps

1. **Create Indexes:** Add indexes for frequently queried fields
2. **Set up Monitoring:** Implement logging and monitoring
3. **Add Validation:** Implement additional document validation
4. **Performance Testing:** Test with realistic data volumes
5. **Security Review:** Review and harden security settings

For more detailed information, refer to the [MongoDB Integration Documentation](mongodb_integration.md).

# MongoDB Integration for RTRDA Project

This document provides comprehensive information about the MongoDB integration implemented in the RTRDA project.

## Overview

The MongoDB integration provides a flexible NoSQL database solution alongside the existing SQL Server database. This allows for:

- Storing unstructured or semi-structured data
- High-performance analytics and logging
- Flexible schema evolution
- Horizontal scaling capabilities

## Architecture

The MongoDB integration consists of several key components:

1. **Connection Manager** (`utils/mongodb_connection.py`) - Manages MongoDB connections with pooling
2. **CRUD Operations** (`utils/mongodb_crud.py`) - Provides comprehensive database operations
3. **Document Models** (`mongodb_app/models.py`) - Defines document structures
4. **Serializers** (`mongodb_app/serializers.py`) - Handles data validation and serialization
5. **API Views** (`mongodb_app/views.py`) - REST API endpoints for MongoDB operations

## Configuration

### Environment Variables

Add the following environment variables to configure MongoDB connection:

```bash
# MongoDB Connection Settings
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=rtrda_mongodb
MONGODB_USERNAME=your_username
MONGODB_PASSWORD=your_password
MONGODB_AUTH_SOURCE=admin

# Connection Pool Settings
MONGODB_MAX_POOL_SIZE=50
MONGODB_MIN_POOL_SIZE=5
MONGODB_MAX_IDLE_TIME_MS=30000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000
MONGODB_SOCKET_TIMEOUT_MS=20000
MONGODB_CONNECT_TIMEOUT_MS=20000
```

### Django Settings

The MongoDB configuration is automatically loaded from `RTRDA/settings.py`:

```python
MONGODB_SETTINGS = {
    'host': os.environ.get('MONGODB_HOST', 'localhost'),
    'port': int(os.environ.get('MONGODB_PORT', 27017)),
    'db': os.environ.get('MONGODB_DB', 'rtrda_mongodb'),
    # ... other settings
}
```

## Document Models

### Available Models

1. **MongoUser** - User data with flexible profile information
2. **MongoLog** - Application logs with detailed context
3. **MongoAnalytics** - Event tracking and analytics data
4. **MongoConfiguration** - Application configuration settings

### Example Usage

```python
from mongodb_app.models import MongoUser

# Create a new user document
user = MongoUser(
    username='john_doe',
    email='<EMAIL>',
    first_name='John',
    last_name='Doe',
    user_type='regular',
    profile_data={'department': 'IT', 'role': 'Developer'},
    preferences={'theme': 'dark', 'language': 'en'}
)

# Validate the document
errors = user.validate()
if not errors:
    # Convert to dictionary for storage
    user_dict = user.to_dict()
```

## API Endpoints

### Base URL Structure

All MongoDB API endpoints are prefixed with `/api/` and follow RESTful conventions:

- `GET /api/mongo-users/` - List all users
- `POST /api/mongo-users/` - Create a new user
- `GET /api/mongo-users/{id}/` - Retrieve a specific user
- `PUT /api/mongo-users/{id}/` - Update a specific user
- `DELETE /api/mongo-users/{id}/` - Delete a specific user

### Available Endpoints

#### MongoUser Endpoints
- `GET /api/mongo-users/` - List users with pagination
- `POST /api/mongo-users/` - Create new user
- `GET /api/mongo-users/{id}/` - Get user by ID
- `PUT /api/mongo-users/{id}/` - Update user
- `DELETE /api/mongo-users/{id}/` - Delete user
- `GET /api/mongo-users/active_users/` - Get active users only
- `POST /api/mongo-users/{id}/deactivate/` - Deactivate user
- `POST /api/mongo-users/bulk_create/` - Create multiple users
- `GET /api/mongo-users/search/` - Search users with filters

#### MongoLog Endpoints
- `GET /api/mongo-logs/` - List logs with pagination
- `POST /api/mongo-logs/` - Create new log entry
- `GET /api/mongo-logs/by_level/?level=ERROR` - Get logs by level
- `GET /api/mongo-logs/errors/` - Get error and critical logs

#### MongoAnalytics Endpoints
- `GET /api/mongo-analytics/` - List analytics events
- `POST /api/mongo-analytics/` - Create new analytics event
- `GET /api/mongo-analytics/by_event_type/?event_type=page_view` - Get events by type
- `GET /api/mongo-analytics/summary/` - Get analytics summary

#### MongoConfiguration Endpoints
- `GET /api/mongo-configurations/` - List configurations
- `POST /api/mongo-configurations/` - Create new configuration
- `GET /api/mongo-configurations/by_category/?category=general` - Get by category
- `GET /api/mongo-configurations/by_key/?key=API_TIMEOUT` - Get by key

### Query Parameters

#### Pagination
- `limit` - Number of results to return (default: no limit)
- `skip` - Number of results to skip (default: 0)

#### Sorting
- `sort_by` - Field to sort by (default: 'created_at')
- `sort_order` - Sort direction: 'asc' or 'desc' (default: 'desc')

#### Search
- Any field name can be used as a query parameter for text search
- Example: `GET /api/mongo-users/search/?username=john&user_type=admin`

## Usage Examples

### Creating Documents

```python
import requests

# Create a new user
user_data = {
    "username": "jane_doe",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Doe",
    "user_type": "admin",
    "profile_data": {
        "department": "Management",
        "role": "Manager"
    }
}

response = requests.post(
    'http://localhost:8000/api/mongo-users/',
    json=user_data,
    headers={'Authorization': 'Bearer your_jwt_token'}
)

if response.status_code == 201:
    created_user = response.json()
    print(f"Created user with ID: {created_user['_id']}")
```

### Querying Documents

```python
# Get all active users
response = requests.get(
    'http://localhost:8000/api/mongo-users/active_users/',
    headers={'Authorization': 'Bearer your_jwt_token'}
)

active_users = response.json()

# Search users by username
response = requests.get(
    'http://localhost:8000/api/mongo-users/search/?username=jane',
    headers={'Authorization': 'Bearer your_jwt_token'}
)

search_results = response.json()
```

### Logging Events

```python
# Create a log entry
log_data = {
    "level": "INFO",
    "message": "User login successful",
    "module": "authentication",
    "user_id": "user_123",
    "ip_address": "*************",
    "extra_data": {
        "login_method": "password",
        "user_agent": "Mozilla/5.0..."
    }
}

response = requests.post(
    'http://localhost:8000/api/mongo-logs/',
    json=log_data,
    headers={'Authorization': 'Bearer your_jwt_token'}
)
```

### Analytics Tracking

```python
# Track a page view event
analytics_data = {
    "event_type": "page_view",
    "event_name": "dashboard_view",
    "user_id": "user_123",
    "properties": {
        "page_title": "Dashboard",
        "section": "main"
    },
    "page_url": "https://example.com/dashboard",
    "ip_address": "*************"
}

response = requests.post(
    'http://localhost:8000/api/mongo-analytics/',
    json=analytics_data,
    headers={'Authorization': 'Bearer your_jwt_token'}
)
```

## Direct Database Operations

For advanced use cases, you can use the CRUD operations directly:

```python
from utils.mongodb_crud import MongoDBCRUD

# Initialize CRUD for a collection
user_crud = MongoDBCRUD('users')

# Create a document
user_data = {
    "username": "direct_user",
    "email": "<EMAIL>"
}
result = user_crud.create_one(user_data)
print(f"Created document with ID: {result.inserted_id}")

# Find documents
users = user_crud.find_many(
    filter_dict={"user_type": "admin"},
    limit=10
)

# Update a document
update_result = user_crud.update_by_id(
    document_id=str(result.inserted_id),
    update_dict={"$set": {"last_login": datetime.utcnow()}}
)

# Delete a document
delete_result = user_crud.delete_by_id(str(result.inserted_id))
```
